import numpy as np
from sklearn.cluster import DBSCAN
from sklearn.linear_model import RANSACRegressor
from sklearn.preprocessing import PolynomialFeatures
from sklearn.pipeline import make_pipeline
import open3d as o3d

def load_point_cloud(file_path):
    """Load point cloud from a PCD file."""
    pcd = o3d.io.read_point_cloud(file_path)
    points = np.asarray(pcd.points)
    intensities = np.asarray(pcd.colors)[:, 0]  # Assuming intensity is in red channel
    return points, intensities

def estimate_ground_plane(points, inlier_threshold=0.1, max_iterations=1000):
    """Estimate ground plane using RANSAC."""
    X = points[:, :2]  # Use x, y coordinates
    y = points[:, 2]   # Height (z) as target
    ransac = RANSACRegressor(residual_threshold=inlier_threshold, max_trials=max_iterations)
    ransac.fit(X, y)
    
    inlier_mask = ransac.inlier_mask_
    ground_points = points[inlier_mask]
    non_ground_points = points[~inlier_mask]
    return ground_points, non_ground_points, ransac

def filter_lane_markings(non_ground_points, intensities, intensity_threshold=0.8):
    """Filter points likely to be lane markings based on intensity."""
    high_intensity_mask = intensities > intensity_threshold
    lane_points = non_ground_points[high_intensity_mask]
    lane_intensities = intensities[high_intensity_mask]
    return lane_points, lane_intensities

def cluster_lane_points(lane_points, eps=0.3, min_samples=10):
    """Cluster lane points using DBSCAN to separate individual lane lines."""
    db = DBSCAN(eps=eps, min_samples=min_samples).fit(lane_points)
    labels = db.labels_
    unique_labels = set(labels) - {-1}  # Exclude noise (-1)
    lane_clusters = [lane_points[labels == label] for label in unique_labels]
    return lane_clusters

def fit_lane_boundaries(lane_clusters, degree=2):
    """Fit polynomial curves to each lane cluster to define boundaries."""
    polyreg = make_pipeline(PolynomialFeatures(degree), RANSACRegressor())
    lane_boundaries = []
    
    for cluster in lane_clusters:
        if len(cluster) < 10:  # Skip small clusters
            continue
        X = cluster[:, 0].reshape(-1, 1)  # x-coordinate
        y = cluster[:, 1]                 # y-coordinate
        polyreg.fit(X, y)
        x_range = np.linspace(min(X), max(X), 100).reshape(-1, 1)
        y_pred = polyreg.predict(x_range)
        lane_boundaries.append((x_range.flatten(), y_pred))
    
    return lane_boundaries

def visualize_results(points, lane_clusters, lane_boundaries):
    """Visualize point cloud, lane clusters, and fitted boundaries."""
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    pcd.paint_uniform_color([0.5, 0.5, 0.5])  # Gray for all points
    
    geometries = [pcd]
    
    # Color lane clusters
    for i, cluster in enumerate(lane_clusters):
        cluster_pcd = o3d.geometry.PointCloud()
        cluster_pcd.points = o3d.utility.Vector3dVector(cluster)
        cluster_pcd.paint_uniform_color([np.random.rand(), np.random.rand(), np.random.rand()])
        geometries.append(cluster_pcd)
    
    # Create lines for lane boundaries
    for x, y in lane_boundaries:
        points = np.vstack((x, y, np.zeros_like(x))).T
        line_set = o3d.geometry.LineSet()
        line_set.points = o3d.utility.Vector3dVector(points)
        line_set.lines = o3d.utility.Vector2iVector([[i, i+1] for i in range(len(points)-1)])
        line_set.paint_uniform_color([1, 0, 0])  # Red for boundaries
        geometries.append(line_set)
    
    o3d.visualization.draw_geometries(geometries)

def main(file_path):
    # Load point cloud
    points, intensities = load_point_cloud(file_path)
    
    # Step 1: Estimate ground plane
    ground_points, non_ground_points, _ = estimate_ground_plane(points)
    
    # Step 2: Filter lane markings based on intensity
    lane_points, _ = filter_lane_markings(non_ground_points, intensities)
    
    # Step 3: Cluster lane points
    lane_clusters = cluster_lane_points(lane_points)
    
    # Step 4: Fit polynomial curves to lane clusters
    lane_boundaries = fit_lane_boundaries(lane_clusters)
    
    # Step 5: Visualize results
    visualize_results(points, lane_clusters, lane_boundaries)
    
    return lane_boundaries

if __name__ == "__main__":
    file_path = "path_to_your_point_cloud.pcd"  # Replace with your PCD file path
    lane_boundaries = main(file_path)